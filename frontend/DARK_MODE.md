# Dark Mode Implementation

This project uses a modern dark mode implementation optimized for Tailwind CSS v4 with the Vite plugin.

## 🌙 How It Works

### Architecture
- **Class-based Dark Mode**: Uses the `dark` class on the `<html>` element
- **Tailwind v4 Compatible**: Configured to work with the modern Tailwind CSS v4 Vite plugin
- **No External Dependencies**: Simple, lightweight implementation without additional libraries

### Features
✅ **Persistent Storage**: User preference saved in localStorage  
✅ **System Preference Detection**: Respects user's OS dark mode setting  
✅ **Smooth Animations**: Beautiful transitions between light/dark modes  
✅ **Accessible**: Proper ARIA labels and keyboard navigation  
✅ **Performance Optimized**: Minimal re-renders and efficient state management  

## 🛠️ Technical Implementation

### Configuration Files

#### `tailwind.config.js`
```javascript
export default {
  darkMode: 'class', // Enables class-based dark mode
  // ... theme configuration
};
```

#### `frontend/src/index.css`
```css
@import "tailwindcss";
/* Custom animations and utilities */
```

### Dark Mode Toggle Component

Located in `frontend/src/components/Navbar.tsx`, the `DarkModeToggle` component:

1. **Initialization**: Checks localStorage first, falls back to system preference
2. **State Management**: Uses React useState with useEffect for side effects
3. **DOM Manipulation**: Adds/removes `dark` class from document element
4. **Persistence**: Saves preference to localStorage on every change

### Usage in Components

```jsx
// Dark mode classes are automatically applied
<div className="bg-white dark:bg-gray-900 text-gray-900 dark:text-white">
  Content that adapts to dark mode
</div>
```

## 🎨 Styling Guidelines

### Color Scheme
- **Light Mode**: Uses white/gray backgrounds with dark text
- **Dark Mode**: Uses dark backgrounds with light text
- **Gradients**: Maintained across both modes with appropriate opacity adjustments

### Best Practices
1. Always provide both light and dark variants for colors
2. Use opacity modifiers for subtle effects (`/10`, `/20`, etc.)
3. Test contrast ratios for accessibility
4. Use `transition-colors duration-300` for smooth color transitions

## 🧪 Testing

The dark mode functionality is tested in `frontend/src/components/__tests__/Navbar.test.tsx`:

- Toggle functionality
- localStorage persistence
- DOM class application
- Accessibility attributes

## 🚀 Future Enhancements

Potential improvements:
- System preference change detection
- Multiple theme options (not just light/dark)
- Theme-specific component variants
- CSS custom properties for dynamic theming

## 🔧 Troubleshooting

### Common Issues

1. **Dark mode not persisting**: Check localStorage in browser dev tools
2. **Styles not applying**: Ensure Tailwind config includes `darkMode: 'class'`
3. **Flashing on load**: Consider adding initial theme detection script to HTML head

### Browser Support
- Modern browsers with localStorage support
- CSS custom properties support
- ES6+ JavaScript features
