// API Response Types based on Django models

export interface SocialLink {
  id: number;
  platform: string;
  url: string;
  order: number;
}

export interface Profile {
  id: number;
  name: string | null;
  role: string | null;
  email: string | null;
  bio: string | null;
  avatar: string | null;
  resume: string | null;
  social_links: SocialLink[];
  created_at: string;
  updated_at: string;
}

export interface Category {
  id: number;
  name: string;
  type: 'project' | 'blog';
}

export interface TypingText {
  id: number;
  text: string;
  order: number;
}

export interface Hero {
  id: number;
  title: string | null;
  subtitle: string | null;
  image: string | null;
  is_active: boolean;
  typing_texts: TypingText[];
  created_at: string;
  updated_at: string;
}

export interface Skill {
  id: number;
  name: string;
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  order: number;
}

export interface About {
  content: ReactNode;
  id: number;
  title: string | null;
  description: string | null;
  image: string | null;
  years_of_experience: number | null;
  is_active: boolean;
  skills: Skill[];
  created_at: string;
  updated_at: string;
}

export interface ExperienceTechnology {
  id: number;
  name: string;
  order: number;
}

export interface Experience {
  id: number;
  company_name: string | null;
  role: string | null;
  start_date: string | null;
  end_date: string | null;
  description: string | null;
  is_active: boolean;
  technologies: ExperienceTechnology[];
  created_at: string;
  updated_at: string;
}

export interface ProjectTechnology {
  id: number;
  name: string;
  order: number;
}

export interface ProjectGalleryImage {
  id: number;
  image: string;
  caption: string | null;
  order: number;
}

export interface Project {
  id: number;
  title: string | null;
  description: string | null;
  short_description: string | null;
  image: string | null;
  category: Category | null;
  live_url: string | null;
  github_url: string | null;
  is_featured: boolean;
  technologies: ProjectTechnology[];
  gallery_images?: ProjectGalleryImage[];
  created_at: string;
  updated_at: string;
}

export interface BlogTag {
  id: number;
  name: string;
  order: number;
}

export interface BlogImage {
  id: number;
  image: string;
  caption: string | null;
  alt_text: string | null;
  order: number;
  is_inline: boolean;
}

export interface BlogCodeSnippet {
  id: number;
  title: string | null;
  code: string;
  language: string;
  description: string | null;
  filename: string | null;
  order: number;
  is_highlighted: boolean;
}

export interface Blog {
  id: number;
  title: string | null;
  slug: string | null;
  content: string | null;
  excerpt: string | null;
  featured_image: string | null;
  is_featured: boolean;
  category: Category | null;
  count: number | null;
  reading_time: number | null;
  difficulty_level: 'beginner' | 'intermediate' | 'advanced' | 'expert' | null;
  is_published: boolean;
  published_at: string | null;
  tags: BlogTag[];
  images?: BlogImage[];
  code_snippets?: BlogCodeSnippet[];
  created_at: string;
  updated_at: string;
}

export interface Contact {
  id: number;
  name: string | null;
  email: string | null;
  subject: string | null;
  phone: string | null;
  preferred_method: 'email' | 'phone' | 'whatsapp' | null;
  message: string | null;
  created_at: string;
  updated_at: string;
}

export interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  phone?: string;
  preferred_method: 'email' | 'phone' | 'whatsapp';
  message: string;
}

export interface HomePageData {
  profile: Profile | null;
  hero: Hero | null;
  about: About | null;
  featured_projects: Project[];
  featured_blogs: Blog[];
  recent_experience: Experience[];
}

export interface ApiResponse<T> {
  data: T;
  message?: string;
}

export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}
