import type {
  Profile,
  Category,
  Hero,
  About,
  Experience,
  Project,
  Blog,
  Contact,
  ContactFormData,
  HomePageData,
  ApiResponse
} from '../types/api';

// Base API configuration
const API_BASE_URL = 'http://localhost:8000/api';

class ApiService {
  private async fetchApi<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;

    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API Error for ${endpoint}:`, error);
      throw error;
    }
  }

  // Homepage data
  async getHomePageData(): Promise<HomePageData> {
    return this.fetchApi<HomePageData>('/home/');
  }

  // Profile endpoints
  async getProfile(): Promise<Profile> {
    return this.fetchApi<Profile>('/profile/');
  }

  async getProfiles(): Promise<Profile[]> {
    return this.fetchApi<Profile[]>('/profiles/');
  }

  // Category endpoints
  async getCategories(type?: 'project' | 'blog'): Promise<Category[]> {
    const params = type ? `?type=${type}` : '';
    return this.fetchApi<Category[]>(`/categories/${params}`);
  }

  // Hero endpoints
  async getActiveHero(): Promise<Hero> {
    return this.fetchApi<Hero>('/hero/');
  }

  // About endpoints
  async getActiveAbout(): Promise<About> {
    return this.fetchApi<About>('/about/');
  }

  // Experience endpoints
  async getExperiences(): Promise<Experience[]> {
    return this.fetchApi<Experience[]>('/experiences/');
  }

  // Project endpoints
  async getProjects(params?: {
    featured?: boolean;
    category?: number;
  }): Promise<Project[]> {
    const searchParams = new URLSearchParams();
    if (params?.featured) searchParams.append('featured', 'true');
    if (params?.category) searchParams.append('category', params.category.toString());

    const queryString = searchParams.toString();
    const endpoint = `/projects/${queryString ? `?${queryString}` : ''}`;

    return this.fetchApi<Project[]>(endpoint);
  }

  async getProject(id: number): Promise<Project> {
    return this.fetchApi<Project>(`/projects/${id}/`);
  }

  // Blog endpoints
  async getBlogs(params?: {
    featured?: boolean;
    category?: number;
    tag?: string;
  }): Promise<Blog[]> {
    const searchParams = new URLSearchParams();
    if (params?.featured) searchParams.append('featured', 'true');
    if (params?.category) searchParams.append('category', params.category.toString());
    if (params?.tag) searchParams.append('tag', params.tag);

    const queryString = searchParams.toString();
    const endpoint = `/blogs/${queryString ? `?${queryString}` : ''}`;

    return this.fetchApi<Blog[]>(endpoint);
  }

  async getBlog(slug: string): Promise<Blog> {
    return this.fetchApi<Blog>(`/blogs/${slug}/`);
  }

  async getBlogById(id: number): Promise<Blog> {
    return this.fetchApi<Blog>(`/blogs/id/${id}/`);
  }

  // Contact endpoints
  async submitContact(data: ContactFormData): Promise<ApiResponse<Contact>> {
    return this.fetchApi<ApiResponse<Contact>>('/contact/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }
}

// Create singleton instance
const apiService = new ApiService();

// Export individual methods with proper binding
export const getHomePageData = () => apiService.getHomePageData();
export const getProfile = () => apiService.getProfile();
export const getProfiles = () => apiService.getProfiles();
export const getCategories = (type?: 'project' | 'blog') => apiService.getCategories(type);
export const getActiveHero = () => apiService.getActiveHero();
export const getActiveAbout = () => apiService.getActiveAbout();
export const getExperiences = () => apiService.getExperiences();
export const getProjects = (params?: { featured?: boolean; category?: number; }) => apiService.getProjects(params);
export const getProject = (id: number) => apiService.getProject(id);
export const getBlogs = (params?: { featured?: boolean; category?: number; tag?: string; }) => apiService.getBlogs(params);
export const getBlog = (slug: string) => apiService.getBlog(slug);
export const getBlogById = (id: number) => apiService.getBlogById(id);
export const submitContact = (data: ContactFormData) => apiService.submitContact(data);

// Also export the service instance if needed
export { apiService };
