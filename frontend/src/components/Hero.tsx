import { useEffect, useState } from 'react';
import type { Hero as HeroType, Profile } from '../types/api';

interface HeroProps {
  hero: HeroType | null;
  profile: Profile | null;
}

export const Hero = ({ hero, profile }: HeroProps) => {
  const [currentTextIndex, setCurrentTextIndex] = useState(0);
  const [displayText, setDisplayText] = useState('');
  const [isTyping, setIsTyping] = useState(true);

  const typingTexts = hero?.typing_texts || ['Developer', 'Designer', 'Creator'];

  useEffect(() => {
    if (typingTexts.length === 0) return;

    const currentText = typingTexts[currentTextIndex];
    let timeoutId: NodeJS.Timeout;

    if (isTyping) {
      if (displayText.length < currentText.length) {
        timeoutId = setTimeout(() => {
          setDisplayText(currentText.slice(0, displayText.length + 1));
        }, 100);
      } else {
        timeoutId = setTimeout(() => {
          setIsTyping(false);
        }, 2000);
      }
    } else {
      if (displayText.length > 0) {
        timeoutId = setTimeout(() => {
          setDisplayText(displayText.slice(0, -1));
        }, 50);
      } else {
        setCurrentTextIndex((prev) => (prev + 1) % typingTexts.length);
        setIsTyping(true);
      }
    }

    return () => clearTimeout(timeoutId);
  }, [displayText, isTyping, currentTextIndex, typingTexts]);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-950 dark:to-black">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="container-custom relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="space-y-8 text-center lg:text-left">
            <div className="space-y-4">
              <h1 className="text-5xl lg:text-7xl font-bold text-gray-900 dark:text-gray-100 leading-tight">
                {hero?.title || `Hi, I'm ${profile?.name || 'Developer'}`}
              </h1>

              <div className="text-2xl lg:text-3xl text-gray-600 dark:text-gray-300 h-12 flex items-center justify-center lg:justify-start">
                <span>I'm a </span>
                <span className="text-gradient font-semibold ml-2 min-w-[200px] text-left">
                  {displayText}
                  <span className="animate-pulse">|</span>
                </span>
              </div>

              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl leading-relaxed">
                {hero?.subtitle || profile?.bio || 'Passionate about creating amazing digital experiences with modern technologies and innovative solutions.'}
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <a
                href="#projects"
                className="btn btn-primary btn-lg group"
              >
                View My Work
                <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </a>

              <a
                href="#contact"
                className="btn btn-outline btn-lg"
              >
                Get In Touch
              </a>
            </div>

            {/* Social Links */}
            {profile?.social_links && profile.social_links.length > 0 && (
              <div className="flex gap-4 justify-center lg:justify-start">
                {profile.social_links.map((link) => (
                  <a
                    key={link.id}
                    href={link.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-12 h-12 bg-white dark:bg-gray-800 rounded-full shadow-md hover:shadow-lg flex items-center justify-center text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-white transition-all duration-200 hover:scale-110"
                  >
                    <span className="text-sm font-medium">
                      {link.platform.charAt(0).toUpperCase()}
                    </span>
                  </a>
                ))}
              </div>
            )}
          </div>

          {/* Image/Avatar */}
          <div className="flex justify-center lg:justify-end">
            <div className="relative">
              {profile?.avatar ? (
                <div className="w-80 h-80 lg:w-96 lg:h-96 rounded-full overflow-hidden shadow-2xl border-8 border-white">
                  <img
                    src={profile.avatar}
                    alt={profile.name || 'Profile'}
                    className="w-full h-full object-cover"
                  />
                </div>
              ) : (
                <div className="w-80 h-80 lg:w-96 lg:h-96 rounded-full bg-gradient-to-br from-primary-400 to-purple-600 shadow-2xl border-8 border-white flex items-center justify-center">
                  <span className="text-6xl lg:text-8xl font-bold text-white">
                    {profile?.name?.charAt(0) || 'D'}
                  </span>
                </div>
              )}

              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 w-8 h-8 bg-primary-500 rounded-full animate-bounce-slow"></div>
              <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-purple-500 rounded-full animate-bounce-slow animation-delay-400"></div>
              <div className="absolute top-1/2 -left-8 w-4 h-4 bg-yellow-400 rounded-full animate-bounce-slow animation-delay-200"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-gray-400 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
};
