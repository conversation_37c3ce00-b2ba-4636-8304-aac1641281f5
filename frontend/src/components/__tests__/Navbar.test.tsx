import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { Navbar } from '../Navbar';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock as any;

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

const NavbarWithRouter = () => (
  <BrowserRouter>
    <Navbar />
  </BrowserRouter>
);

describe('Navbar', () => {
  beforeEach(() => {
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    document.documentElement.classList.remove('dark');
  });

  it('renders navbar with navigation items', () => {
    render(<NavbarWithRouter />);
    
    expect(screen.getByText('Portfolio')).toBeInTheDocument();
    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('About')).toBeInTheDocument();
    expect(screen.getByText('Projects')).toBeInTheDocument();
    expect(screen.getByText('Blog')).toBeInTheDocument();
    expect(screen.getByText('Contact')).toBeInTheDocument();
  });

  it('renders dark mode toggle button', () => {
    render(<NavbarWithRouter />);
    
    const darkModeButton = screen.getByLabelText(/switch to/i);
    expect(darkModeButton).toBeInTheDocument();
  });

  it('toggles dark mode when dark mode button is clicked', () => {
    render(<NavbarWithRouter />);
    
    const darkModeButton = screen.getByLabelText(/switch to/i);
    
    // Initially should be light mode
    expect(darkModeButton).toHaveAttribute('aria-label', 'Switch to dark mode');
    
    // Click to toggle to dark mode
    fireEvent.click(darkModeButton);
    
    // Should now be dark mode
    expect(darkModeButton).toHaveAttribute('aria-label', 'Switch to light mode');
    expect(document.documentElement.classList.contains('dark')).toBe(true);
  });

  it('saves dark mode preference to localStorage', () => {
    render(<NavbarWithRouter />);
    
    const darkModeButton = screen.getByLabelText(/switch to/i);
    
    // Click to enable dark mode
    fireEvent.click(darkModeButton);
    
    // Check if localStorage.setItem was called
    expect(localStorageMock.setItem).toHaveBeenCalledWith('darkMode', 'true');
  });
});
