# app/urls.py (for your portfolio app)
from django.urls import path
from .views import (
    ProfileListView, ProfileDetailView,
    CategoryListView, HeroActiveView, AboutActiveView,
    ExperienceListView, ProjectListView, ProjectDetailView,
    BlogListView, BlogDetailView, BlogDetailByIdView,
    ContactCreateView, HomePageDataView
)

app_name = 'portfolio'

urlpatterns = [
    # Homepage combined data
    path('api/home/', HomePageDataView.as_view(), name='home-data'),
    
    # Profile
    path('api/profile/', ProfileDetailView.as_view(), name='profile-detail'),
    path('api/profiles/', ProfileListView.as_view(), name='profile-list'),
    
    # Categories
    path('api/categories/', CategoryListView.as_view(), name='category-list'),
    
    # Hero
    path('api/hero/', HeroActiveView.as_view(), name='hero-active'),
    
    # About
    path('api/about/', AboutActiveView.as_view(), name='about-active'),
    
    # Experience
    path('api/experiences/', ExperienceListView.as_view(), name='experience-list'),
    
    # Projects
    path('api/projects/', ProjectListView.as_view(), name='project-list'),
    path('api/projects/<int:pk>/', ProjectDetailView.as_view(), name='project-detail'),
    
    # Blogs
    path('api/blogs/', BlogListView.as_view(), name='blog-list'),
    path('api/blogs/<slug:slug>/', BlogDetailView.as_view(), name='blog-detail-slug'),
    path('api/blogs/id/<int:pk>/', BlogDetailByIdView.as_view(), name='blog-detail-id'),
    
    # Contact
    path('api/contact/', ContactCreateView.as_view(), name='contact-create'),
]