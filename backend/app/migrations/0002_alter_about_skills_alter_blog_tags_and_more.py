# Generated by Django 5.2.6 on 2025-09-12 06:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0001_initial'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='about',
            name='skills',
            field=models.J<PERSON><PERSON>ield(blank=True, default=list),
        ),
        migrations.Alter<PERSON>ield(
            model_name='blog',
            name='tags',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.Alter<PERSON>ield(
            model_name='experience',
            name='technologies',
            field=models.J<PERSON><PERSON>ield(blank=True, default=list),
        ),
        migrations.AlterField(
            model_name='hero',
            name='typing_text',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.Alter<PERSON>ield(
            model_name='profile',
            name='social_links',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AlterField(
            model_name='project',
            name='gallery_images',
            field=models.J<PERSON><PERSON>ield(blank=True, default=list),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name='project',
            name='technologies',
            field=models.<PERSON><PERSON><PERSON><PERSON>(blank=True, default=list),
        ),
    ]
