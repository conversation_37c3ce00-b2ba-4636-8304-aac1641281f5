# Generated by Django 5.2.6 on 2025-09-12 06:55

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0003_remove_about_skills_remove_blog_tags_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='blog',
            name='difficulty_level',
            field=models.CharField(blank=True, choices=[('beginner', 'Beginner'), ('intermediate', 'Intermediate'), ('advanced', 'Advanced'), ('expert', 'Expert')], max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='blog',
            name='is_published',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='blog',
            name='published_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='blog',
            name='reading_time',
            field=models.IntegerField(blank=True, help_text='Estimated reading time in minutes', null=True),
        ),
        migrations.AlterField(
            model_name='blog',
            name='title',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.CreateModel(
            name='BlogCodeSnippet',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(blank=True, help_text='Optional title for the code snippet', max_length=200, null=True)),
                ('code', models.TextField(help_text='The actual code content')),
                ('language', models.CharField(choices=[('python', 'Python'), ('javascript', 'JavaScript'), ('typescript', 'TypeScript'), ('html', 'HTML'), ('css', 'CSS'), ('scss', 'SCSS'), ('java', 'Java'), ('cpp', 'C++'), ('c', 'C'), ('csharp', 'C#'), ('php', 'PHP'), ('ruby', 'Ruby'), ('go', 'Go'), ('rust', 'Rust'), ('swift', 'Swift'), ('kotlin', 'Kotlin'), ('sql', 'SQL'), ('bash', 'Bash'), ('powershell', 'PowerShell'), ('json', 'JSON'), ('xml', 'XML'), ('yaml', 'YAML'), ('markdown', 'Markdown'), ('dockerfile', 'Dockerfile'), ('nginx', 'Nginx'), ('apache', 'Apache'), ('other', 'Other')], default='python', max_length=20)),
                ('description', models.TextField(blank=True, help_text='Optional description or explanation', null=True)),
                ('filename', models.CharField(blank=True, help_text='Optional filename', max_length=100, null=True)),
                ('order', models.PositiveIntegerField(default=0)),
                ('is_highlighted', models.BooleanField(default=False, help_text='Whether to highlight this snippet')),
                ('blog', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='code_snippets', to='app.blog')),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='BlogImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='blog_images/')),
                ('caption', models.CharField(blank=True, max_length=300, null=True)),
                ('alt_text', models.CharField(blank=True, help_text='Alt text for accessibility', max_length=200, null=True)),
                ('order', models.PositiveIntegerField(default=0)),
                ('is_inline', models.BooleanField(default=False, help_text='Whether this image appears inline with content')),
                ('blog', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='app.blog')),
            ],
            options={
                'ordering': ['order'],
            },
        ),
    ]
