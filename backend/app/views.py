from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.db.models import F
from .models import (
    Profile, Category, Hero, About, Experience,
    Project, Blog, Contact
)
from .serializers import (
    ProfileSerializer, CategorySerializer, HeroSerializer,
    AboutSerializer, ExperienceSerializer, ProjectListSerializer,
    ProjectDetailSerializer, BlogListSerializer, BlogDetailSerializer,
    ContactSerializer
)

# Profile Views
class ProfileListView(generics.ListAPIView):
    queryset = Profile.objects.all()
    serializer_class = ProfileSerializer

class ProfileDetailView(generics.RetrieveAPIView):
    queryset = Profile.objects.all()
    serializer_class = ProfileSerializer
    
    def get_object(self):
        # Return the first profile (for single-person portfolio)
        return Profile.objects.first()

# Category Views
class CategoryListView(generics.ListAPIView):
    queryset = Category.objects.all()
    serializer_class = CategorySerializer
    
    def get_queryset(self):
        queryset = super().get_queryset()
        category_type = self.request.query_params.get('type', None)
        if category_type:
            queryset = queryset.filter(type=category_type)
        return queryset

# Hero Views
class HeroActiveView(generics.RetrieveAPIView):
    serializer_class = HeroSerializer
    
    def get_object(self):
        return Hero.objects.filter(is_active=True).first()

# About Views
class AboutActiveView(generics.RetrieveAPIView):
    serializer_class = AboutSerializer
    
    def get_object(self):
        return About.objects.filter(is_active=True).first()

# Experience Views
class ExperienceListView(generics.ListAPIView):
    serializer_class = ExperienceSerializer
    
    def get_queryset(self):
        return Experience.objects.filter(is_active=True).order_by('-start_date')

# Project Views
class ProjectListView(generics.ListAPIView):
    serializer_class = ProjectListSerializer
    
    def get_queryset(self):
        queryset = Project.objects.all().select_related('category')
        
        # Filter by featured
        is_featured = self.request.query_params.get('featured', None)
        if is_featured:
            queryset = queryset.filter(is_featured=True)
        
        # Filter by category
        category_id = self.request.query_params.get('category', None)
        if category_id:
            queryset = queryset.filter(category_id=category_id)
            
        return queryset.order_by('-created_at')

class ProjectDetailView(generics.RetrieveAPIView):
    queryset = Project.objects.all().select_related('category')
    serializer_class = ProjectDetailSerializer

# Blog Views
class BlogListView(generics.ListAPIView):
    serializer_class = BlogListSerializer
    
    def get_queryset(self):
        queryset = Blog.objects.all().select_related('category')
        
        # Filter by featured
        is_featured = self.request.query_params.get('featured', None)
        if is_featured:
            queryset = queryset.filter(is_featured=True)
        
        # Filter by category
        category_id = self.request.query_params.get('category', None)
        if category_id:
            queryset = queryset.filter(category_id=category_id)
        
        # Filter by tag
        tag = self.request.query_params.get('tag', None)
        if tag:
            queryset = queryset.filter(tags__contains=tag)
            
        return queryset.order_by('-created_at')

class BlogDetailView(generics.RetrieveAPIView):
    queryset = Blog.objects.all().select_related('category')
    serializer_class = BlogDetailSerializer
    lookup_field = 'slug'
    
    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        # Increment view count
        Blog.objects.filter(pk=instance.pk).update(count=F('count') + 1)
        instance.refresh_from_db()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

class BlogDetailByIdView(generics.RetrieveAPIView):
    queryset = Blog.objects.all().select_related('category')
    serializer_class = BlogDetailSerializer
    
    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        # Increment view count
        Blog.objects.filter(pk=instance.pk).update(count=F('count') + 1)
        instance.refresh_from_db()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

# Contact Views
class ContactCreateView(generics.CreateAPIView):
    queryset = Contact.objects.all()
    serializer_class = ContactSerializer
    
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(
            {'message': 'Contact form submitted successfully!', 'data': serializer.data},
            status=status.HTTP_201_CREATED,
            headers=headers
        )

# Combined API View for Homepage
class HomePageDataView(APIView):
    def get(self, request):
        data = {
            'profile': None,
            'hero': None,
            'about': None,
            'featured_projects': [],
            'featured_blogs': [],
            'recent_experience': []
        }
        
        # Get profile
        profile = Profile.objects.first()
        if profile:
            data['profile'] = ProfileSerializer(profile).data
        
        # Get active hero
        hero = Hero.objects.filter(is_active=True).first()
        if hero:
            data['hero'] = HeroSerializer(hero).data
        
        # Get active about
        about = About.objects.filter(is_active=True).first()
        if about:
            data['about'] = AboutSerializer(about).data
        
        # Get featured projects
        featured_projects = Project.objects.filter(is_featured=True).select_related('category')[:6]
        data['featured_projects'] = ProjectListSerializer(featured_projects, many=True).data
        
        # Get featured blogs
        featured_blogs = Blog.objects.filter(is_featured=True).select_related('category')[:3]
        data['featured_blogs'] = BlogListSerializer(featured_blogs, many=True).data
        
        # Get recent experience
        recent_experience = Experience.objects.filter(is_active=True).order_by('-start_date')[:3]
        data['recent_experience'] = ExperienceSerializer(recent_experience, many=True).data
        
        return Response(data)