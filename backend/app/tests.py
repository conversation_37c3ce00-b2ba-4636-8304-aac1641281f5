from django.test import TestCase
from .models import Blog, BlogTag, BlogImage, BlogCodeSnippet, Category

class BlogModelTest(TestCase):
    def setUp(self):
        # Create a test category
        self.category = Category.objects.create(name="Tech", type="blog")

        # Create a test blog
        self.blog = Blog.objects.create(
            title="Test Tech Blog",
            slug="test-tech-blog",
            content="This is a test blog about technology",
            excerpt="A test blog",
            difficulty_level="intermediate",
            reading_time=5,
            is_published=True
        )

    def test_blog_creation(self):
        """Test that blog is created correctly"""
        self.assertEqual(self.blog.title, "Test Tech Blog")
        self.assertEqual(self.blog.difficulty_level, "intermediate")
        self.assertTrue(self.blog.is_published)

    def test_blog_tag_creation(self):
        """Test that blog tags can be added"""
        tag1 = BlogTag.objects.create(blog=self.blog, name="Django", order=0)
        tag2 = BlogTag.objects.create(blog=self.blog, name="Python", order=1)

        self.assertEqual(self.blog.tags.count(), 2)
        self.assertEqual(self.blog.tags.first().name, "Django")

    def test_blog_code_snippet_creation(self):
        """Test that code snippets can be added"""
        code_snippet = BlogCodeSnippet.objects.create(
            blog=self.blog,
            title="Hello World",
            code="print('Hello, World!')",
            language="python",
            description="A simple hello world example",
            filename="hello.py",
            order=0
        )

        self.assertEqual(self.blog.code_snippets.count(), 1)
        self.assertEqual(code_snippet.language, "python")
        self.assertIn("Hello World", str(code_snippet))

    def test_blog_string_representation(self):
        """Test the string representation of blog"""
        self.assertEqual(str(self.blog), "Test Tech Blog")
