from django.db import models

class Profile (models.Model):
    id = models.BigAutoField(primary_key=True)
    name = models.Char<PERSON>ield(max_length=100, null=True, blank=True)
    role = models.CharField(max_length=100, null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    bio = models.TextField(null=True, blank= True)
    avatar = models.ImageField(null=True, blank=True)
    resume = models.FileField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name or f"Profile {self.id}"

class SocialLink(models.Model):
    profile = models.ForeignKey(Profile, on_delete=models.CASCADE, related_name='social_links')
    platform = models.CharField(max_length=50)  # e.g., 'LinkedIn', 'GitHub', 'Twitter'
    url = models.URLField()
    order = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['order']

    def __str__(self):
        return f"{self.platform} - {self.profile.name}"

class Category(models.Model):
    name = models.CharField(max_length=50)
    type = models.CharField(max_length=20, choices=(
        ('project', 'Project'), 
        ('blog', 'Blog')
    ))

class Hero (models.Model):
    id = models.BigAutoField(primary_key=True)
    title = models.CharField(max_length=100, null=True, blank=True)
    subtitle = models.CharField(max_length=100, null=True, blank=True)
    image = models.ImageField(null=True, blank=True)
    is_active = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title or f"Hero {self.id}"

class TypingText(models.Model):
    hero = models.ForeignKey(Hero, on_delete=models.CASCADE, related_name='typing_texts')
    text = models.CharField(max_length=200)
    order = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['order']

    def __str__(self):
        return f"{self.text} - {self.hero.title}"

class About (models.Model):
    id = models.BigAutoField(primary_key=True)
    title = models.CharField(max_length=100, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    image = models.ImageField(null=True, blank=True)
    years_of_experience = models.IntegerField(null=True, blank=True)
    is_active = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title or f"About {self.id}"

class Skill(models.Model):
    about = models.ForeignKey(About, on_delete=models.CASCADE, related_name='skills')
    name = models.CharField(max_length=100)
    level = models.CharField(max_length=20, choices=[
        ('beginner', 'Beginner'),
        ('intermediate', 'Intermediate'),
        ('advanced', 'Advanced'),
        ('expert', 'Expert')
    ], default='intermediate')
    order = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['order']

    def __str__(self):
        return f"{self.name} ({self.level}) - {self.about.title}"

class Experience (models.Model):
    id = models.BigAutoField(primary_key=True)
    company_name = models.CharField(max_length=100, null=True, blank=True)
    role = models.CharField(max_length=100, null=True, blank=True)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    is_active = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.role} at {self.company_name}"

class ExperienceTechnology(models.Model):
    experience = models.ForeignKey(Experience, on_delete=models.CASCADE, related_name='technologies')
    name = models.CharField(max_length=100)
    order = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['order']

    def __str__(self):
        return f"{self.name} - {self.experience.company_name}"

class Project (models.Model):
    id = models.BigAutoField(primary_key=True)
    title = models.CharField(max_length=100, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    short_description = models.TextField(null=True, blank=True)
    image = models.ImageField(null=True, blank=True)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, null=True, blank=True)
    live_url = models.URLField(null=True, blank=True)
    github_url = models.URLField(null=True, blank=True)
    is_featured = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title or f"Project {self.id}"

class ProjectTechnology(models.Model):
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='technologies')
    name = models.CharField(max_length=100)
    order = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['order']

    def __str__(self):
        return f"{self.name} - {self.project.title}"

class ProjectGalleryImage(models.Model):
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='gallery_images')
    image = models.ImageField(upload_to='project_gallery/')
    caption = models.CharField(max_length=200, null=True, blank=True)
    order = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['order']

    def __str__(self):
        return f"Gallery Image {self.id} - {self.project.title}"

class Blog (models.Model):
    id = models.BigAutoField(primary_key=True)
    title = models.CharField(max_length=200, null=True, blank=True)  # Increased for tech blog titles
    slug = models.SlugField(null=True, blank=True)
    content = models.TextField(null=True, blank=True)
    excerpt = models.TextField(null=True, blank=True)
    featured_image = models.ImageField(null=True, blank=True)
    is_featured = models.BooleanField(default=False)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, null=True, blank=True)
    count = models.IntegerField(null=True, blank=True)
    # New fields for tech blogs
    reading_time = models.IntegerField(null=True, blank=True, help_text="Estimated reading time in minutes")
    difficulty_level = models.CharField(max_length=20, choices=[
        ('beginner', 'Beginner'),
        ('intermediate', 'Intermediate'),
        ('advanced', 'Advanced'),
        ('expert', 'Expert')
    ], null=True, blank=True)
    is_published = models.BooleanField(default=False)
    published_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title or f"Blog {self.id}"

class BlogTag(models.Model):
    blog = models.ForeignKey('Blog', on_delete=models.CASCADE, related_name='tags')
    name = models.CharField(max_length=50)
    order = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['order']

    def __str__(self):
        return f"{self.name} - {self.blog.title}"

class BlogImage(models.Model):
    blog = models.ForeignKey('Blog', on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to='blog_images/')
    caption = models.CharField(max_length=300, null=True, blank=True)
    alt_text = models.CharField(max_length=200, null=True, blank=True, help_text="Alt text for accessibility")
    order = models.PositiveIntegerField(default=0)
    is_inline = models.BooleanField(default=False, help_text="Whether this image appears inline with content")

    class Meta:
        ordering = ['order']

    def __str__(self):
        return f"Image {self.id} - {self.blog.title}"

class BlogCodeSnippet(models.Model):
    LANGUAGE_CHOICES = [
        ('python', 'Python'),
        ('javascript', 'JavaScript'),
        ('typescript', 'TypeScript'),
        ('html', 'HTML'),
        ('css', 'CSS'),
        ('scss', 'SCSS'),
        ('java', 'Java'),
        ('cpp', 'C++'),
        ('c', 'C'),
        ('csharp', 'C#'),
        ('php', 'PHP'),
        ('ruby', 'Ruby'),
        ('go', 'Go'),
        ('rust', 'Rust'),
        ('swift', 'Swift'),
        ('kotlin', 'Kotlin'),
        ('sql', 'SQL'),
        ('bash', 'Bash'),
        ('powershell', 'PowerShell'),
        ('json', 'JSON'),
        ('xml', 'XML'),
        ('yaml', 'YAML'),
        ('markdown', 'Markdown'),
        ('dockerfile', 'Dockerfile'),
        ('nginx', 'Nginx'),
        ('apache', 'Apache'),
        ('other', 'Other'),
    ]

    blog = models.ForeignKey('Blog', on_delete=models.CASCADE, related_name='code_snippets')
    title = models.CharField(max_length=200, null=True, blank=True, help_text="Optional title for the code snippet")
    code = models.TextField(help_text="The actual code content", blank=True)
    language = models.CharField(max_length=20, choices=LANGUAGE_CHOICES, default='python')
    description = models.TextField(null=True, blank=True, help_text="Optional description or explanation")
    filename = models.CharField(max_length=100, null=True, blank=True, help_text="Optional filename")
    order = models.PositiveIntegerField(default=0)
    is_highlighted = models.BooleanField(default=False, help_text="Whether to highlight this snippet")

    class Meta:
        ordering = ['order']

    def __str__(self):
        title = self.title or f"Code Snippet {self.id}"
        return f"{title} ({self.language}) - {self.blog.title}"

class Contact (models.Model):
    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=100, null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    subject = models.CharField(max_length=100, null=True, blank=True)
    phone = models.CharField(max_length=100, null=True, blank=True)
    preferred_method = models.CharField(max_length=100, null=True, blank=True, choices=(('email', 'Email'), ('phone', 'Phone'), ('whatsapp', 'Whatsapp')))
    message = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

